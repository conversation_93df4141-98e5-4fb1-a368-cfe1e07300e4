import { Component, Input, ViewChild, ElementRef, OnInit } from '@angular/core';
import { ManagedAccountService } from '../../managed-account.service';
import { DownloadTemplate } from '../../models/commentary.model';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { UploadDataModalComponent } from '../../../shared/upload-data-modal/upload-data-modal.component';

@Component({
  selector: 'app-managed-account-data-table',
  templateUrl: './managed-account-data-table.component.html',
  styleUrls: ['./managed-account-data-table.component.scss']
})
export class ManagedAccountDataTableComponent implements OnInit {
  @Input() tableTitle: string = '';
  @Input() data: any[] = [];
  @Input() tableName: string = '';
  @Input() permissions: { [subFeature: string]: { canView: boolean; canEdit: boolean; canImport?: boolean; canExport?: boolean } };
  footnotes = [
    { name: "FootNote", newComment: "", isExpanded: false, isEdit: false },
  ];
  @Input() companyID: string = '';
  @Input() moduleName: string = '';
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef;

  constructor(
    private managedAccountService: ManagedAccountService,
    private _miscService: MiscellaneousService,
    private modalService: NgbModal,
    private toastrService: ToastrService
  ) {}

  ngOnInit() {
  }

  // File handling methods
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.handleFileUpload(file);
    }
  }

  openUpload(): void {
    if (!this.permissions?.[this.tableName]?.canImport) {
      this.toastrService.error('You do not have permission to import data', '', {
        positionClass: 'toast-center-center',
      });
      return;
    }

    const dialogRef = this.modalService.open(UploadDataModalComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    dialogRef.componentInstance.tableName = this.tableName;
    dialogRef.componentInstance.companyID = this.companyID;

    dialogRef.componentInstance.fileUploaded.subscribe((file: File) => {
      this.handleFileUpload(file);
    });

    dialogRef.componentInstance.downloadTemplate.subscribe(() => {
      this.downloadTemplate();
    });
  }

  handleFileUpload(file: File): void {
    if (!file) return;

    // Security check for file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const allowedTypes = ['xlsx', 'xls'];

    if (!allowedTypes.includes(fileExtension || '')) {
      this.toastrService.error('Invalid file type. Please upload Excel files only.');
      return;
    }

    // File size check (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      this.toastrService.error('File size exceeds 5MB limit');
      return;
    }

    // TODO: Implement actual file upload to server
    // This would typically call a service method to upload the file
    console.log('Uploading file:', file);
    this.toastrService.success(`File ${file.name} uploaded successfully`, '', {
      positionClass: 'toast-center-center'
    });
  }

  downloadTemplate(): void {
    if (!this.companyID || !this.moduleName) {
      console.error('Company ID and Module ID are required for template download');
      return;
    }
    const template: DownloadTemplate = { managedAccountId: this.companyID, moduleName: this.moduleName };
    this.managedAccountService.downloadTemplate(template).subscribe({
      next: (response) => {
        // Create blob from response
        this._miscService.downloadExcelFile(response);
      },
      error: (error) => {
        console.error('Error downloading template:', error);
        // You can add user-friendly error handling here
      }
    });
  }

  // Export functionality
  exportToExcel(): void {
    // TODO: Implement Excel export
  }

  // Menu handling methods
  openMenu(event: any): void {
    // TODO: Implement menu opening logic
  }

  onSelect(event: any): void {
    // TODO: Implement menu selection handling
  }
}
