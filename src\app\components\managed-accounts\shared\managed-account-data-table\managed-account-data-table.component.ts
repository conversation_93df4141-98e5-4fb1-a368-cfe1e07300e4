import { Component, Input, ViewChild, ElementRef, OnInit } from '@angular/core';
import { ManagedAccountService } from '../../managed-account.service';
import { DownloadTemplate } from '../../models/commentary.model';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';

@Component({
  selector: 'app-managed-account-data-table',
  templateUrl: './managed-account-data-table.component.html',
  styleUrls: ['./managed-account-data-table.component.scss']
})
export class ManagedAccountDataTableComponent implements OnInit {
  @Input() tableTitle: string = '';
  @Input() data: any[] = [];
  @Input() tableName: string = '';
  @Input() permissions: { [subFeature: string]: { canView: boolean; canEdit: boolean; canImport?: boolean; canExport?: boolean } };
  footnotes = [
    { name: "FootNote", newComment: "", isExpanded: false, isEdit: false },
  ];
  @Input() companyID: string = '';
  @Input() moduleName: string = '';
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef;

  constructor(private managedAccountService: ManagedAccountService,
    private _miscService: MiscellaneousService
  ) {}

  ngOnInit() {
  }

  // File handling methods
  onFileSelected(event: any): void {
    // TODO: Implement file selection logic
  }

  openUpload(): void {
    // TODO: Implement upload functionality
  }

  downloadTemplate(): void {
    if (!this.companyID || !this.moduleName) {
      console.error('Company ID and Module ID are required for template download');
      return;
    }
    const template: DownloadTemplate = { managedAccountId: this.companyID, moduleName: this.moduleName };
    this.managedAccountService.downloadTemplate(template).subscribe({
      next: (response) => {
        // Create blob from response
        this._miscService.downloadExcelFile(response);
      },
      error: (error) => {
        console.error('Error downloading template:', error);
        // You can add user-friendly error handling here
      }
    });
  }

  // Export functionality
  exportToExcel(): void {
    // TODO: Implement Excel export
  }

  // Menu handling methods
  openMenu(event: any): void {
    // TODO: Implement menu opening logic
  }

  onSelect(event: any): void {
    // TODO: Implement menu selection handling
  }
}
