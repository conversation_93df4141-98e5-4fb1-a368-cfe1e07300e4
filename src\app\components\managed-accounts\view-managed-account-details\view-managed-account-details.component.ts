import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { ManagedAccount } from '../managed-account.model';
import { ToastrService } from 'ngx-toastr';
import { ManagedAccountConstants, ManagedAccountPermissionConstants, PermissionActions, TOASTER_MSG } from 'src/app/common/constants';
import { FeaturesEnum } from 'src/app/services/permission.service';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';
import { Subscription } from 'rxjs';
import { ManagedAccountService } from '../managed-account.service';
import { INavigationTabLink } from '../shared/navigationLink.model';
import { CommonSubFeaturePermissionService } from "src/app/services/subPermission.service";
import { PageConfigurationService } from 'src/app/services/page-configuration.service';
import { PageConfigurationPageDetails } from 'src/app/common/enums';

@Component({
  selector: 'app-view-managed-account-details',
  templateUrl: './view-managed-account-details.component.html',
  styleUrls: ['./view-managed-account-details.component.scss']
})
export class ViewManagedAccountDetailsComponent {
 
  ManagedAccountConstant = ManagedAccountConstants;

  public navigationTabLinks: INavigationTabLink[] = [];
  public tabs: any[] = [];
  public subPageList: any[] = [];
  private tabNameMapping: { [key: string]: string } = {};

  subscription: Subscription;
  isLoading: boolean = true;
  managedAccountId: string;
  accountData: ManagedAccount;
  uamId: number;
  permissions: { [subFeature: string]: { canView: boolean; canEdit: boolean; canImport:boolean; canExport:boolean } } = {};
  subPagefieldList: any;
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private readonly toastrService: ToastrService,
    private managedAccountService: ManagedAccountService,
    private breadcrumbService: BreadcrumbService,
    private subPermissionService: CommonSubFeaturePermissionService,
    private pageConfigurationService: PageConfigurationService,
  ) { }

  selectedTab: string = this.ManagedAccountConstant.TAB_NAMES.Investment_Page;

  isInvestmentPageVisible(): boolean {
    return (
      this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.ManagedAccountFacts]?.canView ||
      this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentSummary]?.canView ||
      this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.Performance]?.canView ||
      this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.NAVData]?.canView ||
      this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.IncomeDistributionsInvestmentPage]?.canView
    );
  }

  // Helper to get first visible tab name
  getFirstVisibleTabName(): string {
    for (const tab of this.navigationTabLinks) {
      if (this.isTabVisible(tab.name)) {
        return tab.name;
      }
    }
    return this.navigationTabLinks[0].name; // fallback
  }

  ngOnInit() { 
    this.isLoading = true;
    this.route.paramMap.subscribe(params => {
      this.managedAccountId = params.get('id');
      this.redirectToAccountData(this.managedAccountId);
    });
    this.getPageConfigSetting();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  private getSubFeatureAccessPermissions() {
    this.subPermissionService.getCommonSubFeatureAccessPermissions(this.uamId.toString(), FeaturesEnum.ManagedAccounts).subscribe({
      next: (result) => this.handleSubFeatureAccessPermissions(result),
      error: (_error) => {
        // Handle error if needed
      }
    });
  }
  private handleSubFeatureAccessPermissions(result: any[]) {
      if (result.length > 0) {
        const subFeatures = ManagedAccountPermissionConstants.ManagedAccountSubFeature;
        Object.keys(subFeatures).forEach(key => {
          const subFeature = subFeatures[key];
          const filtered = result.filter(x => x.subFeature == subFeature);
          this.permissions[subFeature] = {
            canView: this.checkPermissionAccess(filtered, PermissionActions.CAN_VIEW),
            canEdit: this.checkPermissionAccess(filtered, PermissionActions.CAN_EDIT),
            canImport: this.checkPermissionAccess(filtered, PermissionActions.CAN_IMPORT),
            canExport: this.checkPermissionAccess(filtered, PermissionActions.CAN_EXPORT)
          };
        });
      }
    // After permissions are set, check if selected tab is hidden
    if (!this.isTabVisible(this.selectedTab)) {
      // Select next available tab
      const nextTab = this.getFirstVisibleTabName();
      this.selectedTab = nextTab;
      this.navigationTabLinks.forEach(navTab => {
        navTab.isSelected = navTab.name === nextTab;
      });
    }
  }
  checkPermissionAccess(permission:any[], permissionType): boolean {
    return permission.map(x => x[permissionType]).includes(true);
  }

  selectedTabData(tab: INavigationTabLink) {
    if (
      tab.name !== this.ManagedAccountConstant.TAB_NAMES.Investment_Page ||
      this.isInvestmentPageVisible()
    ) {
      this.selectedTab = tab.name;
      this.navigationTabLinks.forEach(navTab => {
        navTab.isSelected = navTab.name === tab.name;
      });
    }
  }

  updateBreadcrumbs(accountName: string) {
    let newBreadcrumbs: any[] = [];
    newBreadcrumbs.push({ label: 'Managed Accounts', url: '/managed-accounts' });
    newBreadcrumbs.push({ label: accountName });
    this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
  }


  redirectToAccountData(managedAccountId: string) {
    this.isLoading = true;
    this.managedAccountService.getManagedAccountById(managedAccountId).subscribe({
      next: (data) => {
        this.isLoading = false;
        if (data == null) {
          this.toastrService.error(TOASTER_MSG.NOT_FOUND, "", { positionClass: TOASTER_MSG.POS_CENTER });
          this.router.navigate(['/managed-accounts']);
          return;
        }
        this.accountData = data;
        this.uamId = data.uamId;
        this.managedAccountId = managedAccountId;
            this.updateBreadcrumbs(this.accountData?.managedAccountName);
        this.getSubFeatureAccessPermissions();
      },
      error: (error) => {
        this.isLoading = false;
        this.toastrService.error('Failed to load managed account details.', "", { positionClass: "toast-center-center" });
        this.router.navigate(['/managed-accounts']);
      }
    });
  }

 


  isTabVisible(tabName: string): boolean {
    if (tabName === ManagedAccountConstants.TAB_NAMES.Investment_Page) {
      return this.isInvestmentPageVisible();
    }
    if (tabName ===  ManagedAccountConstants.TAB_NAMES.Track_Record) {
      return (
        this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.PerformanceAttribution]?.canView ||
        this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.TrackRecord]?.canView
      );
    }
    if (tabName === ManagedAccountConstants.TAB_NAMES.Portfolio_Statistics) {
      const subFeatures = [
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.PortfolioStatistics,
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentLimits,
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.Top5Sectors,
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.Top5Issuers,
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.AssetRatings,
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.AssetClasses,
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.Currencies,
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.Geographies,
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.Sectors
      ];
      return subFeatures.some(subFeature => this.permissions[subFeature]?.canView);
    }
    if (tabName === ManagedAccountConstants.TAB_NAMES.Investment_Portfolio) {
      return this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentPortfolio]?.canView;
    }
    if (tabName === ManagedAccountConstants.TAB_NAMES.Investor_Cashflow_Activity) {
      return (
        this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.CapitalActivity]?.canView ||
        this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.IncomeDistributionInvestorCashflow]?.canView
      );
    }
    if (tabName === ManagedAccountConstants.TAB_NAMES.Commentaries) {
      return (
        this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.MarketCommentary]?.canView ||
        this.permissions[ManagedAccountPermissionConstants.ManagedAccountSubFeature.ManagedAccountCommentary]?.canView
      );
    }
    return true;
  }
  getPageConfigSetting(): void {
    this.pageConfigurationService.getPageConfigSettingById(13).subscribe(
      (result: any) => {
        if (result && result.subPageList) {
          this.subPageList = result.subPageList.filter((x: any) => x.isActive);
          this.subPagefieldList = result.fieldValueList.filter((x: any) => x.isActive);

          this.tabs = result.subPageList
            .filter((x: any) => x.isActive)
            .map((x: any) => ({
              name: x.displayName, 
              aliasname: x.name, 
              hidden: !x.isActive,              
            }));
            this.navigationTabLinks = this.tabs.map((tab, index) => ({
            name: tab.aliasname,
            aliasName: tab.name, 
            tableName: tab.name.toLowerCase(),
            isSelected: index === 0
          }));
            if (this.navigationTabLinks.length > 0) {
            this.selectedTab = this.navigationTabLinks[0].name;
          }
        }                
      }
    );
  }
}