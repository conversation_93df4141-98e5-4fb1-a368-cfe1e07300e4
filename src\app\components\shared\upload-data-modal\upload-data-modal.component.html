<kendo-dialog
  title="Upload Data"
  (close)="closeModal()"
  [width]="700"
  [height]="340"
  class="upload-dialogue"
>
  <div class="d-flex justify-content-between align-items-start">
    <div class="col-md-6 d-flex flex-column text-left vertical-divider">
      <p class="text-danger font-weight-bold upload-header">Important Note</p>
      <ul class="font-size-12">
        <li>
          Follow the format laid out in the template. Do not modify any headers.
        </li>
        <li>File size limit is 20MB.</li>
        <li>For KPIs, allowed symbols are %, $ for KPI data.</li>
      </ul>
      <div class="d-flex align-items-center">
        <span>Click Here to</span>
        <button
          class="btn btn-link p-0 template-download ml-1 font-weight-bold"
          (click)="onDownloadTemplate()"
        >
          Download
        </button>
        <div class="ml-1"></div>
        <div>the Template</div>
      </div>
    </div>

    <div class="col-md-6 d-flex flex-column align-items-center">
      <div
        *ngIf="uploadedFiles?.length === 0"
        appDnd
        (fileDropped)="onFileDropped($event)"
      >
        <img
          src="assets/dist/images/Drag-Files.svg"
          alt="drag files here"
          class="mb-2"
        />
      </div>
      <div *ngIf="uploadedFiles?.length > 0" class="w-100 zero-state-height">
        <div
          class="d-flex align-items-center w-100 p-2 mt-4"          
          [ngClass]="{'invalid-file': invalidFile, 'valid-file': !invalidFile}"
        >
          <div class="mb-0 flex-grow-1 d-flex align-items-center">
            <img
              src="assets/dist/images/Excel_file.svg"
              alt="Excel file"
              class="mr-2"
            />
            <div class="d-flex flex-column mr-2 uploaded-file-style">
              <div
                class="text-nowrap clo-word-wrap default-font-style"
                [attr.title]="uploadedFiles[0].name"
              >
                {{ uploadedFiles[0].name.split(".")[0] }}
              </div>
              <div class="default-font-style">{{ fileSize }} KB</div>
            </div>
          </div>
          <div *ngIf="isUploading" class="w-50 ms-3 mr-2">
            <kendo-progressbar
              [animation]="{ duration: 10000 }"
              [style]="{ backgroundColor: '#4061C7', height: '10px' }"
              [label]="false"
            ></kendo-progressbar>
          </div>
          <img
            *ngIf="!invalidFile"
            id="clo-remove-upload-file"
            alt="remove-file"
            src="assets/dist/images/clo_close.svg"
            class="me-2"
            (click)="removeFile()"
            (keypress)="removeFile()"
          />
          <div *ngIf="invalidFile" class="d-flex">
            <img
              id="clo-remove-upload-file"
              alt="remove-file"
              src="assets/dist/images/clo-warning.svg"
              class="mr-1"
              (click)="removeFile()"
              (keypress)="removeFile()"
            />
            <span class="text-danger">Invalid File</span>
          </div>
        </div>
      </div>
      <div class="mb-2 text-center">
        <span class="font-weight-bold">Drag file</span> to attach
      </div>
      <button
        kendoButton
        (click)="fileInput.click()"
        themeColor="primary"
        fillMode="outline"
        [disabled]="uploadedFiles?.length > 0 && !invalidFile"
        class="d-flex align-items-center justify-content-center"
        style="cursor: pointer"
      >
        <input
          #fileInput
          type="file"
          style="display: none"
          accept=".xlsx,.xls"
          (change)="onBrowsed($event)"
        />
        <img
          src="assets/dist/images/Browse.svg"
          alt="browse file"
          class="browse-icon"
        />
        Browse File
      </button>
    </div>
  </div>
  <kendo-dialog-actions>
    <div class="d-flex justify-content-end w-100">
      <button kendoButton (click)="closeModal()" class="mr-2">Cancel</button>

      <button
        kendoButton
        id="upload-performance-data"
        (click)="onFileSelected()"
        themeColor="primary"
        class="d-flex align-items-center justify-content-center mr-2"
        [disabled]="uploadedFiles?.length === 0"
      >
        <img
          src="assets/dist/images/Upload.svg"
          alt="upload template"
          [ngClass]="
            uploadedFiles?.length === 0
              ? 'upload-image-disabled'
              : 'upload-image-enabled'
          "
        />
        Upload
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
