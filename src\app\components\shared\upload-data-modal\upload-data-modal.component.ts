import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-upload-data-modal',
  templateUrl: './upload-data-modal.component.html',
  styleUrls: ['./upload-data-modal.component.scss']
})
export class UploadDataModalComponent implements OnInit {
  @Input() tableName: string = '';
  @Input() companyID: string = '';
  @Output() fileUploaded = new EventEmitter<File>();
  @Output() downloadTemplate = new EventEmitter<void>();

  uploadedFiles: File[] = [];
  isUploading: boolean = false;
  fileSize: number = 0;
  invalidFile: boolean = false;

  constructor(
    private readonly modalService: NgbModal,
    private readonly toastrService: ToastrService
  ) { }

  ngOnInit(): void {
  }

  onFileSelected(): void {
    const file = this.uploadedFiles[0];
    if (file) {
      this.fileUploaded.emit(file);
      this.modalService.dismissAll();
    }
  }

  closeModal(): void {
    this.modalService.dismissAll();
    this.uploadedFiles = [];
  }

  onBrowsed(event: any): void {
    const file = event.target.files[0];
    this.processFile(file);
  }

  onFileDropped(files: any[]): void {
    const file = files[0];
    this.processFile(file);
  }

  processFile(file: File): void {
    this.isUploading = true;
    this.invalidFile = false;
    
    if (file) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const fileSizeInKB = (file.size / 1024).toFixed(2);
      const fileSizeInMB = file.size / (1024 * 1024);
      
      if (fileSizeInMB > 20) {
        this.invalidFile = true;
        this.isUploading = false;
        return;
      }
      
      if (fileExtension === 'xlsx' || fileExtension === 'xls') {
        this.uploadedFiles = [file];
        this.fileSize = parseFloat(fileSizeInKB);
        this.isUploading = false;
        console.log('Selected file:', file);
      } else {
        this.toastrService.warning('Please select only Excel files');
        this.isUploading = false;
      }
    }
  }

  removeFile(): void {
    this.uploadedFiles = [];
    this.invalidFile = false;
  }

  onDownloadTemplate(): void {
    this.downloadTemplate.emit();
  }
}
